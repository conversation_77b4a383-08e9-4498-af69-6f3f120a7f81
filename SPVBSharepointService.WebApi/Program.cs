using System;
using System.Text;
using FluentValidation;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using Microsoft.Net.Http.Headers;
using Microsoft.OpenApi.Models;
using SPVBSharepointService.WebApi.Repositories.FacialID;
using SPVBSharepointService.WebApi.Repositories.Sharepoints;
using SPVBSharepointService.WebApi.Repositories.Tokens;
using SPVBSharepointService.WebApi.Repositories.Users;
using SPVBSharepointService.WebApi.ViewModels.System.User;

var builder = WebApplication.CreateBuilder(args);

// Cấu hình logging
builder.Logging.ClearProviders(); // Xóa các providers mặc định
builder.Logging.AddConsole(options =>
{
    options.TimestampFormat = "[yyyy-MM-dd HH:mm:ss] "; // Định dạng thời gian
});

builder.Services.AddHttpClient();
builder.Services.AddHttpClient<IUserRepository, UserRepository>(c =>
{
    c.BaseAddress = new Uri(builder.Configuration["ApiSettings:GatewayAddress"]);
    c.DefaultRequestHeaders.Add(HeaderNames.Accept, "application/json; odata=nometadata");
});

builder.Services.AddTransient<IUserRepository, UserRepository>();
builder.Services.AddSingleton<ISharepointRepository, SharepointRepository>();
builder.Services.AddSingleton<IFacialIDRepository, FacialIDRepository>();
builder.Services.AddSingleton<IJWTManagerRepository, JWTManagerRepository>();
builder.Services.AddMemoryCache();

builder.Services.AddScoped<IValidator<RegisterRequest>, RegisterRequestValidator>();
builder.Services.AddControllers();

builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo { Title = "Contractor", Version = "v1" });

    c.AddSecurityDefinition(
        "Bearer",
        new OpenApiSecurityScheme
        {
            Description =
                "JWT Authorization header using the Bearer scheme. \r\n\r\n"
                + "Enter 'Bearer' [space] and then your token in the text input below. \r\n\r\n"
                + "Example: 'Bearer 12345abcef'",
            Name = "Authorization",
            In = ParameterLocation.Header,
            Type = SecuritySchemeType.ApiKey,
            Scheme = "Bearer",
        }
    );

    c.AddSecurityRequirement(
        new OpenApiSecurityRequirement()
        {
            {
                new OpenApiSecurityScheme
                {
                    Reference = new OpenApiReference
                    {
                        Type = ReferenceType.SecurityScheme,
                        Id = "Bearer",
                    },
                    Scheme = "oauth2",
                    Name = "Bearer",
                    In = ParameterLocation.Header,
                },
                new List<string>()
            },
        }
    );
});

var issuer = builder.Configuration.GetValue<string>("JWT:Issuer");
var signingKey = builder.Configuration.GetValue<string>("JWT:Key");
byte[] signingKeyBytes = Encoding.UTF8.GetBytes(signingKey);
builder
    .Services.AddAuthentication(opt =>
    {
        opt.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
        opt.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
    })
    .AddJwtBearer(options =>
    {
        options.RequireHttpsMetadata = false;
        options.SaveToken = true;
        options.TokenValidationParameters =
            new Microsoft.IdentityModel.Tokens.TokenValidationParameters()
            {
                ValidateIssuer = true,
                ValidIssuer = issuer,
                ValidateAudience = true,
                ValidAudience = issuer,
                ValidateLifetime = true,
                ValidateIssuerSigningKey = true,
                ClockSkew = TimeSpan.Zero,
                IssuerSigningKey = new SymmetricSecurityKey(signingKeyBytes),
            };
    });

// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();

builder.Services.AddCors(options =>
{
    options.AddPolicy(
        "CorsPolicy",
        builder => builder.WithOrigins("*").AllowAnyMethod().AllowAnyHeader()
    );
});

var app = builder.Build();

// Configure the HTTP request pipeline.
// use swagger in production as api document
//if (app.Environment.IsDevelopment())
//{
//    app.UseSwagger();
//    app.UseSwaggerUI();
//}
app.UseSwagger();
app.UseSwaggerUI(c =>
{
    if (app.Environment.IsDevelopment() || app.Environment.IsProduction())
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "My Test1 Api v1");
        // Add this line, it will work for you
        c.RoutePrefix = string.Empty;
    }
});

// app.UseHttpsRedirection();

app.UseCors("CorsPolicy");

app.UseAuthentication();

app.UseAuthorization();

app.MapControllers();

app.Run();
