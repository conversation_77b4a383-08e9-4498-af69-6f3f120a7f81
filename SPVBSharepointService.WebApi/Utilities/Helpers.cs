using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Nodes;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace SPVBSharepointService.WebApi.Utilities
{
    public static class Helpers
    {
        public static JsonObject MakeJsonObject(object obj)
        {
            return (JsonObject)JsonNode.Parse(JsonConvert.SerializeObject(obj))!;
        }

        public static string Base64Only(string fullBase64)
        {
            if (string.IsNullOrEmpty(fullBase64))
            {
                return fullBase64; // Return as-is if the string is null or empty
            }

            // Check if the string contains "base64," and split accordingly
            var base64Marker = "base64,";
            return fullBase64.Contains(base64Marker)
                ? fullBase64.Split(base64Marker)[1]
                : fullBase64;
        }
    }
}
