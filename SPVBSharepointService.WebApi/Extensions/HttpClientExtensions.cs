﻿using System;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text.Json;
using System.Text.Json.Nodes;
using System.Threading.Tasks;
using Newtonsoft.Json.Linq;

namespace SPVBSharepointService.WebApi.Extensions
{
    public static class HttpClientExtensions
    {
        public static async Task<T> ReadContentAs<T>(this HttpResponseMessage response)
        {
            if (!response.IsSuccessStatusCode)
                throw new ApplicationException(
                    $"Something went wrong calling the API: {response.ReasonPhrase}"
                );

            var dataAsString = await response.Content.ReadAsStringAsync().ConfigureAwait(false);

            return JsonSerializer.Deserialize<T>(
                dataAsString,
                new JsonSerializerOptions { PropertyNameCaseInsensitive = true }
            );
        }

        public static async Task<T> ReadSharepointItemAs<T>(this HttpResponseMessage response)
        {
            if (!response.IsSuccessStatusCode)
                throw new ApplicationException(
                    $"Something went wrong calling the API: {response.ReasonPhrase}"
                );

            var dataAsString = await response.Content.ReadAsStringAsync().ConfigureAwait(false);

            return JsonSerializer.Deserialize<T>(
                dataAsString,
                new JsonSerializerOptions { PropertyNameCaseInsensitive = true }
            );
        }

        public static async Task<List<T>> ReadSharepointListItemAs<T>(
            this HttpResponseMessage response
        )
        {
            if (!response.IsSuccessStatusCode)
                throw new ApplicationException(
                    $"Something went wrong calling the API: {response.ReasonPhrase}"
                );

            var dataAsString = await response.Content.ReadAsStringAsync().ConfigureAwait(false);

            var valuesAsString = JObject.Parse(dataAsString)["value"]?.ToString();

            return JsonSerializer.Deserialize<List<T>>(valuesAsString ?? "[]") ?? new();
        }

        public static async Task<T> ReadSharepointLookUpAs<T>(this HttpResponseMessage response)
        {
            if (!response.IsSuccessStatusCode)
                throw new ApplicationException(
                    $"Something went wrong calling the API: {response.ReasonPhrase}"
                );

            var dataAsString = await response.Content.ReadAsStringAsync().ConfigureAwait(false);

            try
            {
                var valuesAsString = JObject.Parse(dataAsString)["value"].ToArray();
                var firstValue = valuesAsString.First().ToString();
                return JsonSerializer.Deserialize<T>(firstValue);
            }
            catch (Exception ex)
            {
                System.Console.WriteLine(ex.Message);
                return default;
            }
        }

        public static Task<HttpResponseMessage> PostAsJson<T>(
            this HttpClient httpClient,
            string url,
            T data
        )
        {
            var dataAsString = JsonSerializer.Serialize(data);
            var content = new StringContent(dataAsString);
            content.Headers.ContentType = new MediaTypeHeaderValue("application/json");

            return httpClient.PostAsync(url, content);
        }

        public static Task<HttpResponseMessage> PutAsJson<T>(
            this HttpClient httpClient,
            string url,
            T data
        )
        {
            var dataAsString = JsonSerializer.Serialize(data);
            var content = new StringContent(dataAsString);
            content.Headers.ContentType = new MediaTypeHeaderValue("application/json");

            return httpClient.PutAsync(url, content);
        }
    }
}
