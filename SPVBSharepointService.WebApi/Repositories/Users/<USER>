﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SPVBSharepointService.WebApi.Entities;
using SPVBSharepointService.WebApi.ViewModels.Common;
using SPVBSharepointService.WebApi.ViewModels.System.Login;
using SPVBSharepointService.WebApi.ViewModels.System.User;

namespace SPVBSharepointService.WebApi.Repositories.Users
{
    public interface IUserRepository
    {
        Task<Account> IsValidUser(LoginRequest request);
        Task<ApiResult<Account>> GetUserById(int id);

        Task<int> AddUserRefreshTokens(UserRefreshToken user);

        Task<UserRefreshToken> GetSavedRefreshTokens(string username, string refreshtoken);

        Task RevokeToken(string username, string refreshtoken);

        Task<bool> ChangePassword(int id, UserSharepointChangePassword request);

        Task<Account> GetUserByName(string userName);
        Task<ApiResult<bool>> Register(RegisterRequest request);
        string TestConfig();
    }
}
