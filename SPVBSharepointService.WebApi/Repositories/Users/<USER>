﻿using System.Text.Json.Nodes;
using Microsoft.Extensions.Caching.Memory;
using Newtonsoft.Json;
using SPVBSharepointService.WebApi.Entities;
using SPVBSharepointService.WebApi.Extensions;
using SPVBSharepointService.WebApi.Repositories.Sharepoints;
using SPVBSharepointService.WebApi.Utilities;
using SPVBSharepointService.WebApi.ViewModels.Common;
using SPVBSharepointService.WebApi.ViewModels.System.Login;
using SPVBSharepointService.WebApi.ViewModels.System.User;

namespace SPVBSharepointService.WebApi.Repositories.Users
{
    public class UserRepository : IUserRepository
    {
        private readonly ISharepointRepository _sharepointHelper;
        private readonly IMemoryCache _cache;
        private readonly IConfiguration _configuration;

        public UserRepository(
            ISharepointRepository sharepointHelper,
            IConfiguration configuration,
            IMemoryCache cache
        )
        {
            _sharepointHelper = sharepointHelper;
            _configuration = configuration;
            _cache = cache;
        }

        public async Task<int> AddUserRefreshTokens(UserRefreshToken user)
        {
            if (user == null)
                throw new ArgumentNullException("");

            _cache.Set(user.UserName, user);

            return 1;
        }

        public async Task<Account> IsValidUser(LoginRequest request)
        {
            var user = await _sharepointHelper.GetItem<Account>(
                _configuration["ApiSettings:GatewayAddress"],
                "Accounts",
                $"filter= UserName eq '{request.UserName}'"
            );

            if (user != null || user != default)
            {
                if (
                    PasswordHasherExtensions.VerifyPassword(
                        request.Password,
                        user.Password,
                        Convert.FromHexString(user.Salt)
                    )
                )
                {
                    return user;
                }
            }

            return null;
        }

        public async Task<UserRefreshToken> GetSavedRefreshTokens(
            string username,
            string refreshtoken
        )
        {
            var token = _cache.Get<UserRefreshToken>(username);

            return token;
        }

        public async Task<ApiResult<Account>> GetUserById(int id)
        {
            try
            {
                var user = await _sharepointHelper.GetItem<Account>(
                    _configuration["ApiSettings:GatewayAddress"],
                    "Accounts",
                    id
                );

                if (user == null)
                    return new ApiErrorResult<Account>($"user {id} is not found");

                return new ApiSuccessResult<Account>(user);
            }
            catch (Exception)
            {
                return new ApiErrorResult<Account>($"user {id} is not found");
            }
        }

        public async Task RevokeToken(string username, string refreshtoken)
        {
            _cache.Remove(username);
        }

        public async Task<bool> ChangePassword(int id, UserSharepointChangePassword request)
        {
            var response = await _sharepointHelper.UpdateItem<UserSharepointChangePassword>(
                _configuration["ApiSettings:GatewayAddress"],
                "Accounts",
                id,
                request
            );

            return response.IsSuccessed;
        }

        public async Task<Account> GetUserByName(string userName)
        {
            var user = await _sharepointHelper.GetItem<Account>(
                _configuration["ApiSettings:GatewayAddress"],
                "Accounts",
                $"filter= UserName eq '{userName}'"
            );

            return user;
        }

        public async Task<ApiResult<bool>> Register(RegisterRequest request)
        {
            if (await GetUserByName(request.UserName) != null)
            {
                return new ApiErrorResult<bool>("Tài khoản đã tồn tại");
            }

            var passwordHash = PasswordHasherExtensions.HashPasword(request.Password, out var salt);

            var account = new Account()
            {
                UserName = request.UserName,
                Password = passwordHash,
                ContractorID = request.ContractorID,
                IsActive = request.IsActive,
                Role = request.Role,
                Salt = Convert.ToHexString(salt)
            };

            var result = await _sharepointHelper.PostItemAsync<Account>(
                $"{_configuration["ApiSettings:GatewayAddress"]}_api/web/lists/getbytitle('Accounts')/items",
                (JsonObject)JsonNode.Parse(JsonConvert.SerializeObject(account))
            );

            if (result.IsSuccessed)
            {
                return new ApiSuccessResult<bool>();
            }

            return new ApiErrorResult<bool>("Đăng ký không thành công");
        }

        public string TestConfig()
        {
            return _configuration["ApiSettings:GatewayAddress"];
        }
    }
}
