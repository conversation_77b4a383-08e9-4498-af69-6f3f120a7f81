using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using Microsoft.Extensions.Caching.Memory;
using SPVBSharepointService.WebApi.Entities.FacialID;
using SPVBSharepointService.WebApi.Utilities;

namespace SPVBSharepointService.WebApi.Repositories.FacialID
{
    public class FacialIDRepository : IFacialIDRepository
    {
        private static readonly string TokenCacheKey = "FACIALID_TOKEN";
        private static readonly int TimeoutInMilliseconds = 15 * 60 * 1000; // Adjust timeout as needed
        private readonly HttpClient _httpClient;
        private readonly IMemoryCache _cache;
        private readonly IConfiguration _config;

        public FacialIDRepository(
            HttpClient httpClient,
            IMemoryCache memoryCache,
            IConfiguration config
        )
        {
            _httpClient = httpClient;
            _httpClient.Timeout = TimeSpan.FromMilliseconds(TimeoutInMilliseconds);
            _cache = memoryCache;
            _config = config;
        }

        public async Task<SetContractorInfoResponse> SetContractorInfoAsync(List<FacialIDInfo> info)
        {
            // Define the API endpoint for setting contractor information
            string url = $"{_config["FacialID:base_url"]}/SetContractorInfoVer2";

            // Prepare the request payload
            var requestData = new { setContractorInfoRequest = info };

            try
            {
                // Use FetchFacialIDAsync to make the API call
                string responseData = await FetchFacialIDAsync(
                    url: url,
                    method: HttpMethod.Post,
                    data: requestData
                );

                // Deserialize the response into a FacialIDResponse object
                var response = JsonSerializer.Deserialize<SetContractorInfoResponse>(responseData);

                // Return the deserialized response
                return response ?? throw new Exception("Failed to deserialize response.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in SetContractorInfoAsync: {ex.Message}");
                throw;
            }
        }

        public async Task<bool> CheckFaceIDAsync(string base64Image)
        {
            // Define the API endpoint for checking the face image
            string url = $"{_config["FacialID:base_url"]}/CheckFaceImage";

            // Prepare the request payload
            var requestData = new
            {
                image = Helpers.Base64Only(base64Image), // Ensure image is properly formatted
            };

            try
            {
                // Use FetchFacialIDAsync to make the API call
                string responseData = await FetchFacialIDAsync(
                    url: url,
                    method: HttpMethod.Post,
                    data: requestData
                );

                // Deserialize the response into a generic object or a specific response type
                var response = JsonSerializer.Deserialize<FacialIDResponse>(responseData);

                // Check if the response indicates success
                return response?.messageKey == "success";
            }
            catch (Exception ex)
            {
                // Handle the error (log it, rethrow, or handle as appropriate)
                Console.WriteLine($"Error in CheckFaceIDAsync: {ex.Message}");
            }

            // Return false if any exception occurs or the check fails
            return false;
        }

        /// <summary>
        /// Logs in to the Facial ID system and stores the token in IMemoryCache.
        /// </summary>
        /// <returns>Authentication token as a string.</returns>
        public async Task<string> LoginFacialIDAsync()
        {
            var loginRequest = new
            {
                userName = _config["FacialID:userName"],
                password = _config["FacialID:password"],
            };

            var jsonContent = new StringContent(
                JsonSerializer.Serialize(loginRequest),
                Encoding.UTF8,
                "application/json"
            );

            var response = await _httpClient.PostAsync(
                $"{_config["FacialID:base_url"]}/auth/login",
                jsonContent
            );
            response.EnsureSuccessStatusCode();

            var responseData = await response.Content.ReadAsStringAsync();
            var loginResponse = JsonSerializer.Deserialize<FacialIDLoginResponse>(responseData);

            var token =
                loginResponse?.data?.token ?? throw new Exception("Failed to retrieve token.");

            // Cache the token with an expiration time
            _cache.Set(TokenCacheKey, token, TimeSpan.FromMinutes(60)); // Adjust expiration as needed

            return token;
        }

        /// <summary>
        /// Fetches data from a Facial ID endpoint with authentication and retry logic.
        /// </summary>
        public async Task<string> FetchFacialIDAsync(
            string url,
            HttpMethod method,
            object? data = null,
            int retryNumber = 0,
            int maxRetries = 3
        )
        {
            if (
                !_cache.TryGetValue(TokenCacheKey, out string? token) || string.IsNullOrEmpty(token)
            )
            {
                token = await LoginFacialIDAsync();
            }

            try
            {
                var request = new HttpRequestMessage(method, url)
                {
                    Content =
                        data != null
                            ? new StringContent(
                                JsonSerializer.Serialize(data),
                                Encoding.UTF8,
                                "application/json"
                            )
                            : null,
                };

                request.Headers.Add("token", token);
                request.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

                var response = await _httpClient.SendAsync(request);
                response.EnsureSuccessStatusCode();

                return await response.Content.ReadAsStringAsync();
            }
            catch (HttpRequestException ex)
                when (retryNumber < maxRetries
                    && ex.StatusCode == System.Net.HttpStatusCode.Forbidden
                )
            {
                Console.WriteLine($"Retrying due to 403 error... Attempt {retryNumber + 1}");
                await LoginFacialIDAsync();
                return await FetchFacialIDAsync(url, method, data, retryNumber + 1, maxRetries);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error fetching data: {ex.Message}");
                throw;
            }
        }
    }

    public class FacialIDLoginResponse
    {
        public FacialIDLoginResponseData? data { get; set; }
    }

    public class FacialIDLoginResponseData
    {
        public string? name { get; set; }
        public string? token { get; set; }
    }
}
