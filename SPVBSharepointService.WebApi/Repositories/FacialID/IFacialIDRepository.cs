using SPVBSharepointService.WebApi.Entities.FacialID;

namespace SPVBSharepointService.WebApi.Repositories.FacialID
{
    public interface IFacialIDRepository
    {
        Task<SetContractorInfoResponse> SetContractorInfoAsync(List<FacialIDInfo> info);
        Task<string> LoginFacialIDAsync();
        Task<string> FetchFacialIDAsync(
            string url,
            HttpMethod method,
            object? data = null,
            int retryNumber = 0,
            int maxRetries = 3
        );
        Task<bool> CheckFaceIDAsync(string base64Image);
    }
}
