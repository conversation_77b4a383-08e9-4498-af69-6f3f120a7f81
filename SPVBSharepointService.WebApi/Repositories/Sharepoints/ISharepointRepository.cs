﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Nodes;
using System.Threading.Tasks;
using SPVBSharepointService.WebApi.ViewModels.Common;

namespace SPVBSharepointService.WebApi.Repositories.Sharepoints
{
    public interface ISharepointRepository
    {
        Task<string> GetAccessToken();
        bool IsValidToken();
        Task<string> GetContextInfo();

        Task<List<T>> GetListItem<T>(string site, string listName, string filterOptions);
        Task<T> GetItem<T>(string site, string listName, int id);
        Task<T> GetItem<T>(string site, string listName, string filterOptions);
        Task<ApiResult<T>> CreateItem<T>(string site, string listName, T item);
        Task<ApiResult<T>> UpdateItem<T>(string site, string listName, int id, object item);
        Task<bool> DeleteItem(string listName, int id);

        Task<T> CreateAttachment<T>(string listName, int id, Stream fileStream);
        Task<T> UpdateAttachment<T>(string listName, int id, Stream fileStream);
        Task<T> GetAttachment<T>(string listName, int id, string fileName);
        Task<bool> DeleteAttachment(string listName, int id, string fileName);
        Task<List<T>> GetListAttachment<T>(string listName, int id);

        Task<List<T>> GetListFile<T>(string folderPath);
        Task<T> GetFileByPath<T>(string folderPath, string fileName);
        Task<T> CreateFile<T>(
            string folderPath,
            Stream fileStream,
            string fileName,
            bool overwrite
        );
        Task<T> UpdateFile<T>(string folderPath, Stream fileStream, string fileName);
        Task<bool> DeleteFile(string folderPath, string fileName);

        Task<ApiResult<object>> GetItemsAsync(string url);
        Task<ApiResult<T>> PostItemAsync<T>(string url, JsonObject item);
        Task<List<BatchItemRes<JsonObject>>> BatchCreateAsync<T>(
            string siteUrl,
            string listTitle,
            List<JsonObject> items
        );
        Task<List<BatchItemRes<JsonObject>>> BatchUpdateAsync<T>(
            string siteUrl,
            string listTitle,
            List<JsonObject> items
        );
        Task<List<BatchItemRes<JsonObject>>> BatchDeleteAsync<T>(
            string siteUrl,
            string listTitle,
            List<JsonObject> items
        );
        Task<ApiResult<T>> PostFileAsync<T>(string url, IFormFile file);
        Task<ApiResult<T>> PutItemAsync<T>(string url, JsonObject item);
        Task<ApiResult<T>> PutFileAsync<T>(string url, IFormFile file);
        Task<ApiResult<T>> DeleteItemAsync<T>(string url);
    }
}
