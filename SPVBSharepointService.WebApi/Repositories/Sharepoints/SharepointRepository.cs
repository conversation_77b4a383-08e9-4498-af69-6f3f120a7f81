﻿using System.Net.Http.Headers;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Text.Json.Nodes;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Identity.Client;
using Newtonsoft.Json;
using SPVBSharepointService.WebApi.Extensions;
using SPVBSharepointService.WebApi.ViewModels.Common;

namespace SPVBSharepointService.WebApi.Repositories.Sharepoints
{
    public class SharepointRepository : ISharepointRepository
    {
        private readonly IMemoryCache _cache;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly IConfiguration _configuration;

        public SharepointRepository(
            IHttpClientFactory httpClientFactory,
            IConfiguration configuration,
            IMemoryCache cache
        )
        {
            _httpClientFactory = httpClientFactory;
            _configuration = configuration;
            _cache = cache;
        }

        public Task<T> CreateAttachment<T>(string listName, int id, Stream fileStream)
        {
            throw new NotImplementedException();
        }

        public Task<T> CreateFile<T>(
            string folderPath,
            Stream fileStream,
            string fileName,
            bool overwrite
        )
        {
            throw new NotImplementedException();
        }

        public Task<bool> DeleteAttachment(string listName, int id, string fileName)
        {
            throw new NotImplementedException();
        }

        public Task<bool> DeleteFile(string folderPath, string fileName)
        {
            throw new NotImplementedException();
        }

        public Task<bool> DeleteItem(string listName, int id)
        {
            throw new NotImplementedException();
        }

        public Task<T> GetAttachment<T>(string listName, int id, string fileName)
        {
            throw new NotImplementedException();
        }

        public Task<string> GetContextInfo()
        {
            throw new NotImplementedException();
        }

        public Task<T> GetFileByPath<T>(string folderPath, string fileName)
        {
            throw new NotImplementedException();
        }

        public async Task<T> GetItem<T>(string site, string listName, int id)
        {
            var accessToken = await GetAccessToken();

            var client = _httpClientFactory.CreateClient();
            client.BaseAddress = new Uri(site);
            client.DefaultRequestHeaders.Add("Accept", "application/json");
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue(
                "Bearer",
                accessToken
            );
            var response = await client.GetAsync(
                $"_api/web/lists/getbytitle('{listName}')/items({id})"
            );

            if (response.IsSuccessStatusCode)
            {
                return await response.ReadSharepointLookUpAs<T>();
            }

            return default;
        }

        public async Task<T> GetItem<T>(string site, string listName, string filterOptions)
        {
            var accessToken = await GetAccessToken();

            var client = _httpClientFactory.CreateClient();
            client.BaseAddress = new Uri(site);
            client.DefaultRequestHeaders.Add("Accept", "application/json");
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue(
                "Bearer",
                accessToken
            );
            var response = await client.GetAsync(
                $"_api/web/lists/getbytitle('{listName}')/items?${filterOptions}"
            );

            if (response.IsSuccessStatusCode)
            {
                return await response.ReadSharepointLookUpAs<T>();
            }

            return default;
        }

        public async Task<List<T>> GetListItem<T>(
            string site,
            string listName,
            string filterOptions
        )
        {
            var accessToken = await GetAccessToken();

            var client = _httpClientFactory.CreateClient();
            client.DefaultRequestHeaders.Add("Accept", "application/json");
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue(
                "Bearer",
                accessToken
            );
            var response = await client.GetAsync(
                $"{site}_api/web/lists/getbytitle('{listName}')/items?${filterOptions}"
            );

            if (response.IsSuccessStatusCode)
            {
                return await response.ReadSharepointListItemAs<T>();
            }
            else
            {
                string resString = await response.Content.ReadAsStringAsync();
                throw new Exception(resString);
            }
        }

        public Task<List<T>> GetListAttachment<T>(string listName, int id)
        {
            throw new NotImplementedException();
        }

        public Task<List<T>> GetListFile<T>(string folderPath)
        {
            throw new NotImplementedException();
        }

        public bool IsValidToken()
        {
            string accessToken = _cache.Get("AccessToken") as string;
            DateTimeOffset? expirationTime =
                _cache.Get("AccessTokenExpirationTime") as DateTimeOffset?;

            if (expirationTime != null && DateTimeOffset.UtcNow < expirationTime)
            {
                // The access token is still valid, return it
                return true;
            }
            return false;
        }

        public Task<T> UpdateAttachment<T>(string listName, int id, Stream fileStream)
        {
            throw new NotImplementedException();
        }

        public Task<T> UpdateFile<T>(string folderPath, Stream fileStream, string fileName)
        {
            throw new NotImplementedException();
        }

        public async Task<ApiResult<T>> UpdateItem<T>(
            string site,
            string listName,
            int id,
            object item
        )
        {
            var accessToken = await GetAccessToken();

            using var client = _httpClientFactory.CreateClient();
            client.BaseAddress = new Uri(site);
            client.DefaultRequestHeaders.Add("Accept", "application/json;odata=nometadata");
            client.DefaultRequestHeaders.Add("X-HTTP-Method", "MERGE");
            client.DefaultRequestHeaders.Add("IF-MATCH", "*");
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue(
                "Bearer",
                accessToken
            );

            var httpContent = new StringContent(
                JsonConvert.SerializeObject(item),
                Encoding.UTF8,
                "application/json"
            );

            var response = await client.PostAsync(
                $"_api/web/lists/getbytitle('{listName}')/items({id})",
                httpContent
            );

            if (response.IsSuccessStatusCode)
            {
                return new ApiSuccessResult<T>();
            }
            return new ApiErrorResult<T>(await response.Content.ReadFromJsonAsync<T>());
        }

        public async Task<ApiResult<T>> CreateItem<T>(string site, string listName, T item)
        {
            var accessToken = await GetAccessToken();

            using var client = _httpClientFactory.CreateClient();
            client.BaseAddress = new Uri(site);
            client.DefaultRequestHeaders.Add("Accept", "application/json;odata=nometadata");
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue(
                "Bearer",
                accessToken
            );

            var httpContent = new StringContent(
                JsonConvert.SerializeObject(item),
                Encoding.UTF8,
                "application/json"
            );

            var response = await client.PostAsync(
                $"_api/web/lists/getbytitle('{listName}')/items",
                httpContent
            );

            if (response.IsSuccessStatusCode)
            {
                return new ApiSuccessResult<T>();
            }
            return new ApiErrorResult<T>(await response.Content.ReadFromJsonAsync<T>());
        }

        #region Update
        public async Task<ApiResult<object>> GetItemsAsync(string url)
        {
            var accessToken = await GetAccessToken();

            var client = _httpClientFactory.CreateClient();
            client.DefaultRequestHeaders.Add("Accept", "application/json;odata=nometadata");
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue(
                "Bearer",
                accessToken
            );
            var response = await client.GetAsync(url);

            if (response.IsSuccessStatusCode)
            {
                var contentType = response.Content.Headers.ContentType.MediaType;
                if (contentType.Equals("application/json", StringComparison.OrdinalIgnoreCase))
                {
                    var result = await response.Content.ReadFromJsonAsync<object>();
                    return new ApiSuccessResult<object>(result);
                }
                else
                {
                    var bytes = await response.Content.ReadAsByteArrayAsync();
                    var fileName = response.Content.Headers.ContentDisposition.FileName;
                    // Do something with the file stream
                    return new ApiSuccessResult<object>(bytes);
                }
            }
            return new ApiErrorResult<object>(response.Content.ReadFromJsonAsync<object>());
        }

        public async Task<ApiResult<T>> PostFileAsync<T>(string url, IFormFile file)
        {
            var accessToken = await GetAccessToken();

            using var client = _httpClientFactory.CreateClient();
            client.DefaultRequestHeaders.Add("Accept", "application/json;odata=nometadata");
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue(
                "Bearer",
                accessToken
            );

            using var streamContent = new StreamContent(file.OpenReadStream());

            var response = await client.PostAsync(url, streamContent);

            return await ProcessResponse<T>(response);
        }

        public async Task<ApiResult<T>> PostItemAsync<T>(string url, JsonObject item)
        {
            var accessToken = await GetAccessToken();

            using var client = _httpClientFactory.CreateClient();
            client.DefaultRequestHeaders.Add("Accept", "application/json;odata=nometadata");
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue(
                "Bearer",
                accessToken
            );

            var httpContent = new StringContent(
                item.ToJsonString(),
                Encoding.UTF8,
                "application/json"
            );

            var response = await client.PostAsync(url, httpContent);

            return await ProcessResponse<T>(response);
        }

        public async Task<ApiResult<T>> PutFileAsync<T>(string url, IFormFile file)
        {
            var accessToken = await GetAccessToken();

            using var client = _httpClientFactory.CreateClient();
            client.DefaultRequestHeaders.Add("Accept", "application/json;odata=nometadata");
            client.DefaultRequestHeaders.Add("X-HTTP-Method", "PUT");
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue(
                "Bearer",
                accessToken
            );

            using var streamContent = new StreamContent(file.OpenReadStream());

            var response = await client.PostAsync(url, streamContent);

            if (response.IsSuccessStatusCode)
            {
                return new ApiSuccessResult<T>();
            }
            return new ApiErrorResult<T>(await response.Content.ReadFromJsonAsync<T>());
        }

        public async Task<ApiResult<T>> PutItemAsync<T>(string url, JsonObject item)
        {
            var accessToken = await GetAccessToken();

            using var client = _httpClientFactory.CreateClient();
            client.DefaultRequestHeaders.Add("Accept", "application/json;odata=nometadata");
            client.DefaultRequestHeaders.Add("X-HTTP-Method", "MERGE");
            client.DefaultRequestHeaders.Add("IF-MATCH", "*");
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue(
                "Bearer",
                accessToken
            );

            var httpContent = new StringContent(
                item.ToJsonString(),
                Encoding.UTF8,
                "application/json"
            );

            var response = await client.PostAsync(url, httpContent);

            if (response.IsSuccessStatusCode)
            {
                return new ApiSuccessResult<T>();
            }
            return new ApiErrorResult<T>(await response.Content.ReadFromJsonAsync<T>());
        }

        public async Task<ApiResult<T>> DeleteItemAsync<T>(string url)
        {
            var accessToken = await GetAccessToken();

            using var client = _httpClientFactory.CreateClient();
            client.DefaultRequestHeaders.Add("Accept", "application/json;odata=nometadata");
            client.DefaultRequestHeaders.Add("X-HTTP-Method", "DELETE");
            client.DefaultRequestHeaders.Add("If-Match", "*");
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue(
                "Bearer",
                accessToken
            );

            var response = await client.DeleteAsync(url);

            if (response.IsSuccessStatusCode)
            {
                return new ApiSuccessResult<T>();
            }
            return new ApiErrorResult<T>(await response.Content.ReadFromJsonAsync<T>());
        }

        private async Task<ApiResult<T>> ProcessResponse<T>(HttpResponseMessage response)
        {
            var content = await response.Content.ReadFromJsonAsync<T>();

            if (response.IsSuccessStatusCode)
            {
                return new ApiSuccessResult<T>(content);
            }
            return new ApiErrorResult<T>(content);
        }

        public async Task<string> GetAccessToken()
        {
            string accessToken = _cache.Get("AccessToken") as string;
            DateTimeOffset? expirationTime =
                _cache.Get("AccessTokenExpirationTime") as DateTimeOffset?;

            if (
                accessToken is not null
                && expirationTime is not null
                && DateTimeOffset.UtcNow < expirationTime
            )
            {
                // The access token is still valid, return it
                return accessToken;
            }

            var clientId = _configuration["SPInfo:client_id"];
            var authority =
                "https://login.microsoftonline.com/" + _configuration["SPInfo:tenant_id"];
            var clientCertificate = new X509Certificate2(
                _configuration["SPInfo:cert_path"],
                _configuration["SPInfo:cert_pass"]
            );
            var scopes = new[] { _configuration["SPInfo:scope"] };

            var cca = ConfidentialClientApplicationBuilder
                .Create(clientId)
                .WithAuthority(new Uri(authority))
                .WithCertificate(clientCertificate)
                .Build();
            AuthenticationResult tokenResult = await cca.AcquireTokenForClient(scopes)
                .ExecuteAsync();

            if (!string.IsNullOrEmpty(tokenResult.AccessToken))
            {
                // Store the new access token and its expiration time in the cache
                _cache.Set("AccessToken", tokenResult.AccessToken, tokenResult.ExpiresOn);
                _cache.Set("AccessTokenExpirationTime", tokenResult.ExpiresOn);
                return tokenResult.AccessToken;
            }

            throw new Exception("Failed to obtain access token.");
        }

        public async Task<List<BatchItemRes<JsonObject>>> BatchCreateAsync<T>(
            string siteUrl,
            string listTitle,
            List<JsonObject> items
        )
        {
            return await BatchRequest<T>(siteUrl, listTitle, "POST", items);
        }

        public async Task<List<BatchItemRes<JsonObject>>> BatchUpdateAsync<T>(
            string siteUrl,
            string listTitle,
            List<JsonObject> items
        )
        {
            return await BatchRequest<T>(siteUrl, listTitle, "PATCH", items);
        }

        public async Task<List<BatchItemRes<JsonObject>>> BatchDeleteAsync<T>(
            string siteUrl,
            string listTitle,
            List<JsonObject> items
        )
        {
            return await BatchRequest<T>(siteUrl, listTitle, "DELETE", items);
        }

        private async Task<List<BatchItemRes<JsonObject>>> BatchRequest<T>(
            string siteUrl,
            string listTitle,
            string method,
            List<JsonObject> items
        )
        {
            var accessToken = await GetAccessToken();

            using var client = _httpClientFactory.CreateClient();
            client.DefaultRequestHeaders.Accept.Add(
                new MediaTypeWithQualityHeaderValue("application/json")
            );
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue(
                "Bearer",
                accessToken
            );

            var batchBoundary = "batch_" + Guid.NewGuid();
            var changesetBoundary = "changeset_" + Guid.NewGuid();

            StringBuilder batchContent = new StringBuilder();

            batchContent.AppendLine($"--{batchBoundary}");
            batchContent.AppendLine("Content-Type: multipart/mixed; boundary=" + changesetBoundary);
            batchContent.AppendLine();

            foreach (var item in items)
            {
                var jsonItem = item.ToJsonString();
                string url = "";
                if (method == "PUT" || method == "DELETE" || method == "PATCH")
                {
                    var itemID = (int)item!["ID"]!;
                    url =
                        $"{method} {siteUrl}_api/web/lists/getbytitle('{listTitle}')/items({itemID}) HTTP/1.1";
                }

                if (method == "POST")
                {
                    url =
                        $"{method} {siteUrl}_api/web/lists/getbytitle('{listTitle}')/items HTTP/1.1";
                }

                batchContent.AppendLine($"--{changesetBoundary}");
                batchContent.AppendLine("Content-Type: application/http");
                batchContent.AppendLine("Content-Transfer-Encoding: binary");
                batchContent.AppendLine();
                batchContent.AppendLine(url);
                batchContent.AppendLine("Content-Type: application/json;odata=verbose");
                batchContent.AppendLine("IF-MATCH: *");
                batchContent.AppendLine();
                batchContent.AppendLine(jsonItem);
                batchContent.AppendLine();
            }

            batchContent.AppendLine($"--{changesetBoundary}--");
            batchContent.AppendLine($"--{batchBoundary}--");

            HttpContent content = new StringContent(batchContent.ToString());
            content.Headers.ContentType = new MediaTypeHeaderValue("multipart/mixed");
            content.Headers.ContentType.Parameters.Add(
                new NameValueHeaderValue("boundary", batchBoundary)
            );

            HttpResponseMessage response = await client.PostAsync($"{siteUrl}_api/$batch", content);

            string responseBody = await response.Content.ReadAsStringAsync();
            var results = ProcessBatchResponse(responseBody, items);

            return results;
        }

        private static List<BatchItemRes<T>> ProcessBatchResponse<T>(
            string responseBody,
            List<T> items
        )
        {
            // Extract batch boundary from the response
            var batchBoundary = responseBody.Substring(2, responseBody.IndexOf("\r\n") - 2);

            // Split response by batch boundary
            string[] batchResponseParts = responseBody.Split(
                new[] { $"--{batchBoundary}" },
                StringSplitOptions.RemoveEmptyEntries
            );
            int itemIndex = 0;
            var results = new List<BatchItemRes<T>>();

            foreach (var part in batchResponseParts)
            {
                if (part.Contains("HTTP/1.1 2"))
                {
                    results.Add(
                        new BatchItemRes<T>
                        {
                            IsSuccess = true,
                            Item = items[itemIndex],
                            Content = part
                        }
                    );
                }
                else if (part.Contains("HTTP/1.1 4"))
                {
                    results.Add(
                        new BatchItemRes<T>
                        {
                            IsSuccess = false,
                            Item = items[itemIndex],
                            Content = part
                        }
                    );
                }
                itemIndex++;
            }

            return results;
        }

        #endregion
    }
}
