namespace SPVBSharepointService.WebApi.Entities.FacialID
{
    public class FacialIDInfo
    {
        public string userId { get; set; }
        public string fullName { get; set; }
        public int gender { get; set; }
        public string? phoneNumber { get; set; }
        public string userIdCard { get; set; }
        public List<string> siteId { get; set; }
        public List<ContractorInfo> contractorList { get; set; }
        public string contractorIdList { get; set; }
        public string noteDetectFail { get; set; }
        public string startPeriodTime { get; set; }
        public string endPeriodTime { get; set; }
        public string faceTemplate1 { get; set; }
    }

    public class ContractorInfo
    {
        public string code { get; set; }
        public string name { get; set; }
    }
}
