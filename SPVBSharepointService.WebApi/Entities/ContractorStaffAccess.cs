﻿using System.Diagnostics.CodeAnalysis;

namespace SPVBSharepointService.WebApi.Entities
{
    public class ContractorStaffAccess
    {
        public int ID { get; set; }
        public string? IdentificationNumber { get; set; }
        public string? ContractorID { get; set; }
        public string? ContractorName { get; set; }
        public string? WorkLocationID { get; set; }
        public string? Gender { get; set; }
        public string? FullName { get; set; }

        public double? StatusCode { get; set; }
        public double? StatusCodeOut { get; set; }

        public double? AvatarImageID { get; set; }

        public double? ContractorStaffID { get; set; }

        public string? ErrorMessage { get; set; }
        public string? ErrorMessageOut { get; set; }
        public DateTime? ModifiedCheckIn { get; set; }
        public DateTime? ModifiedCheckOut { get; set; }
    }
}
