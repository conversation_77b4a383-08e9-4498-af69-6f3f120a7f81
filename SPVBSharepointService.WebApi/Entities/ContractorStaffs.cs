namespace SPVBSharepointService.WebApi.Entities
{
    public class ContractorStaffs
    {
        public int ID { get; set; }
        public double? AvatarImageID { get; set; }
        public string? IdentificationNumber { get; set; }
        public string? ContractorID { get; set; }
        public string? ContractorName { get; set; }
        public string? Gender { get; set; }
        public string? FullName { get; set; }
        public string? Status { get; set; }
        public string? EHSTrainingList { get; set; }
        public string? QualificationList { get; set; }
        public string? ApprovalDegreeCertificate { get; set; }
        public string? MainTrainingWorkLocationID { get; set; }
        public string? HMPEvalutionStatus { get; set; }
        public string? CTPEvalutionStatus { get; set; }
        public string? QNPEvalutionStatus { get; set; }
        public string? BNPEvalutionStatus { get; set; }
        public string? DOPEvalutionStatus { get; set; }
        public string? W006EvalutionStatus { get; set; }
        public string? W007EvalutionStatus { get; set; }
        public string? W008EvalutionStatus { get; set; }
        public string? W009EvalutionStatus { get; set; }
        public string? W010EvalutionStatus { get; set; }
        public DateTime? DueDate { get; set; }
    }
}
