﻿using System.Net.Http.Headers;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Text.Json.Nodes;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Identity.Client;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SPVBSharepointService.WebApi.Repositories.Sharepoints;

// For more information on enabling Web API for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860

namespace SPVBSharepointService.WebApi.Controllers
{
    [Route("api/v1/[controller]")]
    [ApiController]
    [Authorize]
    public class SharepointsController : ControllerBase
    {
        private readonly ISharepointRepository _sharepointRepository;

        public SharepointsController(ISharepointRepository sharepointRepository)
        {
            _sharepointRepository = sharepointRepository;
        }

        // GET: api/<SharepointsController>
        [HttpGet]
        [ProducesResponseType(typeof(JsonObject), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(byte[]), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(JsonObject), StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> Get(string url)
        {
            var result = await _sharepointRepository.GetItemsAsync(url);

            if (result.IsSuccessed)
            {
                try
                {
                    var bytes = (byte[])result.ResultObj;
                    return File(bytes, "application/octet-stream");
                }
                catch (Exception)
                {
                    return Ok(result.ResultObj);
                }
            }

            return BadRequest(result.ResultObj);
        }

        // POST api/<SharepointsController>/item?url={url}
        [HttpPost("item")]
        [ProducesResponseType(typeof(JsonObject), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(JsonObject), StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> Post(string url, [FromBody] JsonObject value)
        {
            var result = await _sharepointRepository.PostItemAsync<JsonObject>(url, value);

            if (result.IsSuccessed)
                return Ok(result.ResultObj);

            return BadRequest(result.ResultObj);
        }

        // PUT api/<SharepointsController>/item?url={url}
        [HttpPut("item")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(typeof(JsonObject), StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> Put(string url, [FromBody] JsonObject value)
        {
            var result = await _sharepointRepository.PutItemAsync<JsonObject>(url, value);

            if (result.IsSuccessed)
                return NoContent();

            return BadRequest(result.ResultObj);
        }

        // POST api/<SharepointsController>/file?url={url}
        [HttpPost("file")]
        [ProducesResponseType(typeof(JsonObject), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(JsonObject), StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> Post(string url, [FromForm] IFormFile file)
        {
            var result = await _sharepointRepository.PostFileAsync<JsonObject>(url, file);

            if (result.IsSuccessed)
                return Ok(result.ResultObj);

            return BadRequest(result.ResultObj);
        }

        // PUT api/<SharepointsController>/file?url={url}
        [HttpPut("file")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(typeof(JsonObject), StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> Put(string url, [FromForm] IFormFile file)
        {
            var result = await _sharepointRepository.PutFileAsync<JsonObject>(url, file);

            if (result.IsSuccessed)
                return NoContent();

            return BadRequest(result.ResultObj);
        }

        // DELETE api/<SharepointsController>/5
        [HttpDelete]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(JsonObject), StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> Delete(string url)
        {
            var result = await _sharepointRepository.DeleteItemAsync<JsonObject>(url);

            if (result.IsSuccessed)
                return Ok();

            return BadRequest(result.ResultObj);
        }

        // POST api/<SharepointsController>/bulkinsert?url={url}
        [HttpPost("items")]
        [ProducesResponseType(typeof(JsonObject), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(JsonObject), StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> Post(
            string siteUrl,
            string listName,
            [FromBody] List<JsonObject> values
        )
        {
            var result = await _sharepointRepository.BatchCreateAsync<JsonObject>(
                siteUrl,
                listName,
                values
            );

            if (result.Find(r => !r.IsSuccess) == null)
                return Ok(result);

            return BadRequest(result);
        }

        // POST api/<SharepointsController>/bulkinsert?url={url}
        [HttpGet("test")]
        [AllowAnonymous]
        public async Task<IActionResult> GetTest()
        {
            var clientId = "0676f6e6-5a60-4bc5-b1ff-748bd6ba2be4";
            var authority =
                "https://login.microsoftonline.com/1b669d37-c786-49a8-9909-0d72cfbe7c33";
            var clientCertificate = new X509Certificate2(
                @"C:\Users\<USER>\Desktop\cert.pfx",
                "12345"
            );
            var scopes = new[] { "https://htpa.sharepoint.com/.default" };

            var cca = ConfidentialClientApplicationBuilder
                .Create(clientId)
                .WithAuthority(new Uri(authority))
                .WithCertificate(clientCertificate)
                .Build();
            var token = "";

            try
            {
                var result = await cca.AcquireTokenForClient(scopes).ExecuteAsync();

                Console.WriteLine($"Access Token: {result.AccessToken}");
                token = result.AccessToken;
                return Ok(result.AccessToken);
            }
            catch (MsalException ex)
            {
                Console.WriteLine($"Error acquiring token: {ex.Message}");
                return Ok("");
            }
        }
    }
}
