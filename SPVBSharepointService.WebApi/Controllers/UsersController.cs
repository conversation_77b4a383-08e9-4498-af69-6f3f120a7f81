﻿using FluentValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SPVBSharepointService.WebApi.Entities;
using SPVBSharepointService.WebApi.Extensions;
using SPVBSharepointService.WebApi.Repositories.Tokens;
using SPVBSharepointService.WebApi.Repositories.Users;
using SPVBSharepointService.WebApi.ViewModels.Common;
using SPVBSharepointService.WebApi.ViewModels.System.Login;
using SPVBSharepointService.WebApi.ViewModels.System.User;

namespace SPVBSharepointService.WebApi.Controllers
{
    [Route("api/v1/[controller]")]
    [ApiController]
    [Authorize]
    public class UsersController : ControllerBase
    {
        private readonly IUserRepository _userService;
        private readonly IJWTManagerRepository _jWTManagerRepository;
        private readonly IValidator<RegisterRequest> _validator;

        public UsersController(
            IUserRepository userService,
            IJWTManagerRepository jWTManagerRepository,
            IValidator<RegisterRequest> validator
        )
        {
            _userService = userService;
            _jWTManagerRepository = jWTManagerRepository;
            _validator = validator;
        }

        [HttpPost("authenticate")]
        [AllowAnonymous]
        //localhost:5001/api/users/authenticate
        public async Task<IActionResult> Authenticate([FromBody] LoginRequest request)
        {
            var user = await _userService.IsValidUser(request);

            if (user == null)
            {
                return BadRequest("Incorrect username or password!");
            }

            var token = _jWTManagerRepository.GenerateToken(request.UserName);

            if (token == null)
            {
                return Unauthorized("Invalid Attempt!");
            }

            // saving refresh token to the db
            UserRefreshToken obj = new UserRefreshToken
            {
                RefreshToken = token.RefreshToken,
                UserName = request.UserName
            };

            await _userService.AddUserRefreshTokens(obj);

            var response = new LoginRequestResponse()
            {
                Token = token,
                User = new UserViewModel()
                {
                    Id = user.Id,
                    UserName = user.UserName,
                    IsActive = user.IsActive,
                    Role = user.Role,
                    ContractorID = user.ContractorID
                }
            };

            return Ok(response);
        }

        [AllowAnonymous]
        [HttpPost("hash")]
        //localhost:5001/api/users/hash
        public IActionResult HashPassword(string rawstring)
        {
            if (rawstring is not null)
            {
                var passwordHash = PasswordHasherExtensions.HashPasword(rawstring, out var salt);

                return Ok(
                    new
                    {
                        RawPassword = rawstring,
                        PasswordHash = passwordHash,
                        Salt = Convert.ToHexString(salt)
                    }
                );
            }

            return BadRequest("password is invalid.");
        }

        [AllowAnonymous]
        [ProducesResponseType(typeof(ApiResult<bool>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(Dictionary<string, string>), StatusCodes.Status400BadRequest)]
        [HttpPost]
        //localhost:5001/api/users
        public async Task<IActionResult> Register([FromBody] RegisterRequest request)
        {
            var validate = await _validator.ValidateAsync(request);

            if (!validate.IsValid)
            {
                return BadRequest(validate.ToDictionary());
            }

            var result = await _userService.Register(request);

            if (!result.IsSuccessed)
                return BadRequest(result.Message);

            return Ok(result);
        }

        [AllowAnonymous]
        [HttpPost("refresh")]
        //localhost:5001/api/users/refreshtoken
        public async Task<IActionResult> RefreshToken([FromBody] Token token)
        {
            var principal = _jWTManagerRepository.GetPrincipalFromExpiredToken(token.AccessToken);
            var username = principal.Identity?.Name;

            //retrieve the saved refresh token from database
            var savedRefreshToken = await _userService.GetSavedRefreshTokens(
                username,
                token.RefreshToken
            );

            if (savedRefreshToken == null || savedRefreshToken.RefreshToken != token.RefreshToken)
            {
                return Unauthorized("Invalid attempt!");
            }

            var newJwtToken = _jWTManagerRepository.GenerateRefreshToken(username);

            if (newJwtToken == null)
            {
                return Unauthorized("Invalid attempt!");
            }

            // saving refresh token to the db
            UserRefreshToken obj = new UserRefreshToken
            {
                RefreshToken = newJwtToken.RefreshToken,
                UserName = username
            };

            await _userService.RevokeToken(username, token.RefreshToken);
            await _userService.AddUserRefreshTokens(obj);

            return Ok(newJwtToken);
        }

        [Authorize]
        [HttpPut("change-password")]
        //localhost:5001/api/users/{id}
        public async Task<IActionResult> ChangePassword(
            [FromBody] UserChangePasswordRequest request
        )
        {
            string? userName = User.Identity?.Name;
            if (string.IsNullOrEmpty(userName))
            {
                return Unauthorized();
            }

            var user = await _userService.GetUserByName(userName);

            if (user == null)
            {
                return BadRequest("User not found!");
            }

            //check old password
            if (
                !PasswordHasherExtensions.VerifyPassword(
                    request.Old,
                    user.Password,
                    Convert.FromHexString(user.Salt)
                )
            )
            {
                return BadRequest("Old password is not correct!");
            }
            //update new password
            var changePasswordModel = new UserSharepointChangePassword()
            {
                Password = PasswordHasherExtensions.HashPasword(request.New, out var newSalt),
                Salt = Convert.ToHexString(newSalt)
            };

            var isSuccess = await _userService.ChangePassword(user.Id, changePasswordModel);
            if (!isSuccess)
            {
                return BadRequest("Change password fail!");
            }
            return NoContent();
        }

        [AllowAnonymous]
        [HttpGet("test-config")]
        //localhost:5001/api/users/test
        public async Task<IActionResult> TestConfig()
        {
            var gateway = _userService.TestConfig();

            return Ok(gateway);
        }
    }
}
