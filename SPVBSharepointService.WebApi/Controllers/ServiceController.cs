using System.Diagnostics.CodeAnalysis;
using System.IO;
using System.Text.Json.Nodes;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SPVBSharepointService.WebApi.Entities;
using SPVBSharepointService.WebApi.Entities.FacialID;
using SPVBSharepointService.WebApi.Repositories.FacialID;
using SPVBSharepointService.WebApi.Repositories.Sharepoints;
using SPVBSharepointService.WebApi.Utilities;
using SPVBSharepointService.WebApi.ViewModels.Common;

namespace SPVBSharepointService.WebApi.Controllers
{
    [Route("api/v1/[controller]")]
    [ApiController]
    public class ServiceController : ControllerBase
    {
        private readonly ISharepointRepository _sharepointRepository;
        private readonly IFacialIDRepository _facialIDRepo;
        private readonly ILogger<ServiceController> _logger;

        public ServiceController(
            ISharepointRepository sharepointRepository,
            IFacialIDRepository facialIDRepo,
            ILogger<ServiceController> logger
        )
        {
            _sharepointRepository = sharepointRepository;
            _facialIDRepo = facialIDRepo;
            _logger = logger;
        }

        #region Checkin
        [HttpPost("SA_ValidateStaffList")]
        public async Task<IActionResult> SA_ValidateStaffList(bool ignoreCheckUpdated = false)
        {
            List<ValidateStaffResponse> validateResList = new();

            try
            {
                List<ProjectStaffs> projectStaffList = await GetAllItems<ProjectStaffs>(
                    "ProjectStaffs"
                );
                projectStaffList = projectStaffList.Where(pS => pS.Status != "Delete").ToList();

                var distinctStaffList = projectStaffList
                    .GroupBy(c => new
                    {
                        c.IdentificationNumber,
                        c.WorkLocationID,
                        c.ContractorID,
                    })
                    .Select(g => g.First())
                    .ToList();

                var staffAccessList = await GetAllItems<ContractorStaffAccess>(
                    "ContractorStaffAccess"
                );

                var slotMappingList =
                    await _sharepointRepository.GetListItem<WorkLocationSlotMapping>(
                        SysConstant.PTW_CSM_SITE,
                        "WorkLocationSlotMapping",
                        $"top=10"
                    );

                var now = DateTime.Now.ToString("yyyy-MM-dd");
                var projectList = await _sharepointRepository.GetListItem<ProjectList>(
                    SysConstant.PTW_CSM_SITE,
                    "ProjectList",
                    $"filter=TimeStart le '{now}' and TimeEnd ge '{now}'&$top=2000"
                );

                foreach (var pStaff in distinctStaffList)
                {
                    ValidateStaffResponse validateRes = await ValidateAndUpdateStaffAccess(
                        pStaff,
                        slotMappingList,
                        projectList,
                        staffAccessList,
                        projectStaffList,
                        ignoreCheckUpdated
                    );
                    validateResList.Add(validateRes);

                    if (validateRes.Status != ValidateStaffStatus.NO_UPDATE)
                    {
                        await Task.Delay(500);
                    }
                }

                // Access not in ProjectStaffs
                var missingResList = await HandleAddMissingAccess(
                    staffAccessList,
                    projectStaffList,
                    validateResList
                );

                var countByStatus = validateResList
                    .GroupBy(res => res.Status)
                    .Select(res => new { Status = res.Key, Count = res.Count() });

                return Ok(new { countByStatus, validateResList });
            }
            catch (System.Exception ex)
            {
                // _ = TrackingFacialIDAsync(ex, "SA_ValidateStaffList", "Error", ex.Message);
                _logger.LogError("SA_ValidateStaffList Error " + ex.Message);
                return Ok(ex.Message);
            }
        }

        [HttpPost("SA_ValidateSingleStaff")]
        public async Task<IActionResult> SA_ValidateSingleStaff([FromBody] ContractorStaffs cStaff)
        {
            try
            {
                var staffAccessList =
                    await _sharepointRepository.GetListItem<ContractorStaffAccess>(
                        SysConstant.PTW_CSM_SITE,
                        "ContractorStaffAccess",
                        $"filter=IdentificationNumber eq '{cStaff.IdentificationNumber}' and ContractorID eq '{cStaff.ContractorID}'&$top=10"
                    );

                var slotMappingList =
                    await _sharepointRepository.GetListItem<WorkLocationSlotMapping>(
                        SysConstant.PTW_CSM_SITE,
                        "WorkLocationSlotMapping",
                        $"top=10"
                    );

                var now = DateTime.Now.ToString("yyyy-MM-dd");
                var projectList = await _sharepointRepository.GetListItem<ProjectList>(
                    SysConstant.PTW_CSM_SITE,
                    "ProjectList",
                    $"filter=ContractorID eq '{cStaff.ContractorID}' and TimeStart le '{now}' and TimeEnd ge '{now}'"
                );

                List<ProjectStaffs> projectStaffList =
                    await _sharepointRepository.GetListItem<ProjectStaffs>(
                        SysConstant.PTW_CSM_SITE,
                        "ProjectStaffs",
                        $"filter=ContractorStaffID eq '{cStaff.ID}'"
                    );
                projectStaffList = projectStaffList.Where(pS => pS.Status != "Delete").ToList();

                var distinctStaffList = projectStaffList
                    .GroupBy(c => new
                    {
                        c.IdentificationNumber,
                        c.WorkLocationID,
                        c.ContractorID,
                    })
                    .Select(g => g.First())
                    .ToList();

                List<ValidateStaffResponse> validateResList = new();
                foreach (var pStaff in distinctStaffList)
                {
                    ValidateStaffResponse validateRes = await ValidateAndUpdateStaffAccess(
                        pStaff,
                        slotMappingList,
                        projectList,
                        staffAccessList,
                        projectStaffList
                    );
                    validateResList.Add(validateRes);
                    await Task.Delay(500);
                }

                // Access not in ProjectStaffs
                await HandleAddMissingAccess(staffAccessList, projectStaffList, validateResList);

                var countByStatus = validateResList
                    .GroupBy(res => res.Status)
                    .Select(res => new { Status = res.Key, Count = res.Count() });

                return Ok(new { countByStatus, validateResList });
            }
            catch (System.Exception ex)
            {
                // _ = TrackingFacialIDAsync(ex, "SA_ValidateSingleStaff", "Error", ex.Message);
                _logger.LogError("SA_ValidateSingleStaff Error " + ex.Message);
                return Ok(ex.Message);
            }
        }

        private async Task<ValidateStaffResponse> ValidateAndUpdateStaffAccess(
            ProjectStaffs pStaff,
            List<WorkLocationSlotMapping> slotMappingList,
            List<ProjectList> projectList,
            List<ContractorStaffAccess> staffAccessList,
            List<ProjectStaffs> projectStaffList,
            bool ignoreCheckUpdated = false
        )
        {
            string updateStatus = ValidateStaffStatus.NO_UPDATE;
            string updateMessage = "";

            try
            {
                List<string> errors = new();

                var slotID = slotMappingList
                    .Find(s => s.WorkLocationID == pStaff.WorkLocationID)
                    ?.SlotID;

                var projectListIDsOfStaff = projectStaffList
                    .Where(p =>
                        p.IdentificationNumber == pStaff.IdentificationNumber
                        && p.ContractorID == pStaff.ContractorID
                        && p.WorkLocationID == pStaff.WorkLocationID
                    )
                    .Select(p => p.ProjectListID)
                    .ToList();

                var hasOnGoingProject = projectList.Find(project =>
                    projectListIDsOfStaff.Find(projectListID =>
                        projectListID == project.ProjectListID
                    ) != null
                );

                if (hasOnGoingProject == null)
                {
                    errors.Add("Nhân viên chưa được sắp xếp vào công việc/dự án tại nhà máy");
                }

                if (pStaff.Status == "Blocked")
                {
                    errors.Add("Nhân viên bị Khóa thẻ nhà thầu do vi phạm an toàn");
                }

                string evaluationStatus = "";

                var evaluationStatusMap = new Dictionary<string, string>
                {
                    { "HMP", pStaff.HMPEvalutionStatus },
                    { "CTP", pStaff.CTPEvalutionStatus },
                    { "DOP", pStaff.DOPEvalutionStatus },
                    { "QNP", pStaff.QNPEvalutionStatus },
                    { "BNP", pStaff.BNPEvalutionStatus },
                    { "W006", pStaff.W006EvalutionStatus },
                    { "W007", pStaff.W007EvalutionStatus },
                    { "W008", pStaff.W008EvalutionStatus },
                    { "W009", pStaff.W009EvalutionStatus },
                    { "W010", pStaff.W010EvalutionStatus },
                };

                if (evaluationStatusMap.ContainsKey(slotID))
                {
                    evaluationStatus = evaluationStatusMap[slotID];
                }

                if (
                    evaluationStatus != "Đạt"
                    && (
                        pStaff.MainTrainingWorkLocationID != pStaff.WorkLocationID
                        || (pStaff.DueDate < DateTime.Now || !pStaff.DueDate.HasValue)
                    )
                )
                {
                    errors.Add("Nhân viên chưa được nhà máy đánh giá huấn luyện");
                }

                if (pStaff.Status == "Blocked")
                {
                    errors.Add("Nhân viên bị Khóa thẻ nhà thầu do vi phạm an toàn");
                }

                if (!pStaff.DueDate.HasValue || pStaff.DueDate < DateTime.Now)
                {
                    errors.Add("Thẻ nhà thầu của nhân viên hết thời hạn cho phép");
                }

                if (pStaff.ApprovalDegreeCertificate != "Đã kiểm tra")
                {
                    errors.Add("Hồ sơ năng lực của nhân viên chưa được thẩm duyệt");
                }

                List<CompetenceItem> trainingItems =
                    JsonConvert.DeserializeObject<List<CompetenceItem>>(
                        pStaff?.EHSTrainingList ?? "[]"
                    ) ?? new();

                List<CompetenceItem> quaItems =
                    JsonConvert.DeserializeObject<List<CompetenceItem>>(
                        pStaff?.QualificationList ?? "[]"
                    ) ?? new();

                foreach (var training in trainingItems)
                {
                    if (
                        training.TypeCertificate == "Thời hạn"
                        && (training.DateOfExpiry < DateTime.Now || !training.DateOfExpiry.HasValue)
                        && !string.IsNullOrEmpty(training.TrainingRequiredName_VIE)
                    )
                    {
                        errors.Add($"Hồ sơ {training.TrainingRequiredName_VIE} đã hết hạn");
                    }
                }

                foreach (var qua in quaItems)
                {
                    if (
                        qua.TypeCertificate == "Thời hạn"
                        && (qua.DateOfExpiry < DateTime.Now || !qua.DateOfExpiry.HasValue)
                        && !string.IsNullOrEmpty(qua.TrainingRequiredName_VIE)
                    )
                    {
                        errors.Add($"Hồ sơ {qua.TrainingRequiredName_VIE} đã hết hạn");
                    }
                }

                string errorMessage = string.Join("<br />", errors);

                var accessItem = staffAccessList.Find(access =>
                    access.ContractorID == pStaff.ContractorID
                    && access.WorkLocationID == pStaff.WorkLocationID
                    && access.IdentificationNumber == pStaff.IdentificationNumber
                );

                // Get access all plants
                var accessAllPlant = staffAccessList
                    .Where(access => access.ContractorStaffID == pStaff.ContractorStaffID)
                    .ToList();

                var newAccess = new ContractorStaffAccess
                {
                    IdentificationNumber = pStaff.IdentificationNumber,
                    ContractorID = pStaff.ContractorID,
                    WorkLocationID = pStaff.WorkLocationID,
                    ContractorStaffID = pStaff.ContractorStaffID,
                    AvatarImageID = pStaff.AvatarImageID,
                    ErrorMessage = errorMessage,
                    ContractorName = pStaff.ContractorName,
                    FullName = pStaff.FullName,
                    Gender = pStaff.Gender,
                    StatusCode = string.IsNullOrEmpty(errorMessage) ? 1 : 0,
                    ErrorMessageOut = accessItem?.ErrorMessageOut,
                    StatusCodeOut = accessItem?.StatusCodeOut ?? 1,
                    ModifiedCheckIn = DateTime.Now,
                    ModifiedCheckOut = DateTime.Now,
                };

                // no access at plant -> create access
                if (accessItem?.ID == null)
                {
                    var apiResult = await _sharepointRepository.CreateItem(
                        SysConstant.PTW_CSM_SITE,
                        "ContractorStaffAccess",
                        newAccess
                    );

                    if (apiResult.IsSuccessed)
                    {
                        updateStatus = ValidateStaffStatus.CREATED;
                        accessAllPlant.Add(newAccess);
                    }
                    else
                    {
                        updateStatus = ValidateStaffStatus.FAILED;
                        updateMessage = apiResult.Message;
                    }
                }
                else
                {
                    // update existing access
                    if (
                        accessItem.StatusCode != newAccess.StatusCode
                        || (accessItem.ErrorMessage + "") != (newAccess.ErrorMessage + "")
                        || accessItem.Gender != newAccess.Gender
                        || accessItem.FullName != newAccess.FullName
                        || accessItem.AvatarImageID != newAccess.AvatarImageID
                        || accessItem.ContractorName != newAccess.ContractorName
                    )
                    {
                        var apiResult =
                            await _sharepointRepository.UpdateItem<ContractorStaffAccess>(
                                SysConstant.PTW_CSM_SITE,
                                "ContractorStaffAccess",
                                accessItem.ID,
                                newAccess
                            );

                        if (apiResult.IsSuccessed)
                        {
                            updateStatus = ValidateStaffStatus.UPDATED;
                        }
                        else
                        {
                            updateStatus = ValidateStaffStatus.FAILED;
                            updateMessage = apiResult.Message;
                        }
                    }
                    else
                    {
                        updateStatus = ValidateStaffStatus.NO_UPDATE;
                    }
                }
            }
            catch (System.Exception ex)
            {
                updateStatus = ValidateStaffStatus.FAILED;
                updateMessage = ex.Message;

                _logger.LogError(
                    $"ValidateAndUpdateStaffAccess Error ContractorStaffID: {pStaff.ContractorStaffID}; Message: {ex.Message}"
                );
            }

            return new ValidateStaffResponse { Status = updateStatus, Message = updateMessage };
        }

        #endregion

        #region FacialID
        [HttpPost("SA_SendAccessToFacialID")]
        public async Task<IActionResult> SA_SendAccessToFacialID(
            [FromBody] SendAccessToFacialID? body
        )
        {
            string filter = "";
            List<ContractorStaffAccess> allStaffAccessList;

            if (!string.IsNullOrEmpty(body?.IdentificationNumber))
            {
                // Lấy trực tiếp theo IdentificationNumber
                filter = $"IdentificationNumber eq '{body.IdentificationNumber}'";
                allStaffAccessList = await GetAllItems<ContractorStaffAccess>(
                    "ContractorStaffAccess",
                    filter
                );
            }
            else if (!string.IsNullOrEmpty(body?.ModifiedFrom))
            {
                // Lấy theo ModifiedFrom/To và distinct IdentificationNumber
                if (string.IsNullOrEmpty(body.ModifiedTo))
                {
                    filter = $"ModifiedCheckIn ge '{body.ModifiedFrom}'";
                }
                else
                {
                    filter =
                        $"ModifiedCheckIn ge '{body.ModifiedFrom}' and ModifiedCheckIn le '{body.ModifiedTo}'";
                }

                var filteredList = await GetAllItems<ContractorStaffAccess>(
                    "ContractorStaffAccess",
                    filter
                );

                // Distinct IdentificationNumber và lấy lại toàn bộ access
                var distinctIds = filteredList
                    .Select(x => x.IdentificationNumber)
                    .Distinct()
                    .ToList();
                if (distinctIds.Any())
                {
                    var identificationFilter = string.Join(
                        " or ",
                        distinctIds.Select(id => $"IdentificationNumber eq '{id}'")
                    );
                    allStaffAccessList = await GetAllItems<ContractorStaffAccess>(
                        "ContractorStaffAccess",
                        identificationFilter
                    );
                }
                else
                {
                    allStaffAccessList = new List<ContractorStaffAccess>();
                }
            }
            else
            {
                // Không có parameter nào -> lấy tất cả
                allStaffAccessList = await GetAllItems<ContractorStaffAccess>(
                    "ContractorStaffAccess",
                    ""
                );
            }

            var sendRes = await SendAccessToFacialIDAsync(allStaffAccessList);
            return Ok(sendRes);
        }

        private async Task<SetContractorInfoResponse> SendAccessToFacialIDAsync(
            List<ContractorStaffAccess> allStaffAccessList,
            bool trackingSuccess = true
        )
        {
            // Get unique contractors
            var contractors = allStaffAccessList
                .Select(g => new ContractorInfo
                {
                    code = g.ContractorID!,
                    name = g.ContractorName!,
                })
                .DistinctBy(c => c.code)
                .ToList();
            var contractorIdList = string.Join(
                ";",
                allStaffAccessList
                    .DistinctBy(c => c.ContractorID)
                    .Select(g => $"{g.ContractorStaffID}_{g.ContractorID}")
            );

            // Group by IdentificationNumber and WorkLocationID to create separate records for each factory
            var groupByIdentificationAndWorkLocation = allStaffAccessList
                .GroupBy(d => new { d.IdentificationNumber, d.WorkLocationID })
                .Select(group =>
                {
                    var defaultInfo = group.First();

                    // Collect error messages for this specific factory
                    var errorMessages = group
                        .Where(g => !string.IsNullOrEmpty(g.ErrorMessage))
                        .Select(g => $"{g.ErrorMessage}")
                        .Where(msg => !string.IsNullOrEmpty(msg))
                        .Distinct();

                    var noteDetectFail = string.Join("<br /><br />", errorMessages);

                    // Filter for valid access items for this factory
                    var validAccessList = group.Where(access => access.StatusCode == 1).ToList();

                    // Set WorkLocationID for this specific factory
                    string workLocationID = defaultInfo.WorkLocationID!;

                    // Create a list with just this factory's WorkLocationID
                    List<string> siteIdList = new List<string>();
                    if (validAccessList.Any())
                    {
                        siteIdList.Add(workLocationID);
                    }
                    else if (!string.IsNullOrEmpty(workLocationID))
                    {
                        siteIdList.Add(workLocationID);
                    }

                    // Determine the end period time
                    string endPeriodTime = !validAccessList.Any()
                        ? DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                        : DateTime.Now.AddYears(1).ToString("yyyy-MM-dd HH:mm:ss");

                    // Create a separate FacialIDInfo record for each factory
                    return new FacialIDInfo
                    {
                        userId = defaultInfo.IdentificationNumber!,
                        fullName = defaultInfo.FullName!,
                        gender = defaultInfo.Gender == "Nam" ? 1 : 2,
                        phoneNumber = "",
                        userIdCard = defaultInfo.IdentificationNumber!,
                        siteId = siteIdList, // Only contains the current factory
                        contractorList = contractors,
                        contractorIdList = contractorIdList,
                        noteDetectFail = noteDetectFail,
                        startPeriodTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                        endPeriodTime = endPeriodTime,
                        faceTemplate1 = "",
                    };
                })
                .ToList();

            foreach (var info in groupByIdentificationAndWorkLocation)
            {
                _logger.LogInformation("SendAccessToFacialID " + JsonConvert.SerializeObject(info));
            }

            try
            {
                // Send to FacialID API
                var setInfo = await _facialIDRepo.SetContractorInfoAsync(
                    groupByIdentificationAndWorkLocation
                );

                if (setInfo.messageKey == "success")
                {
                    if (trackingSuccess)
                    {
                        _logger.LogInformation(
                            "SetContractorInfo Success "
                                + JsonConvert.SerializeObject(groupByIdentificationAndWorkLocation)
                        );
                    }
                    return setInfo;
                }
                else
                {
                    // Error DATA
                    var errorData = setInfo
                        .data?.listError?.Where(record =>
                            record.message != "faceTemplate1-not-empty"
                        )
                        .ToList();

                    if (errorData != null && errorData.Any())
                    {
                        _logger.LogError(
                            "SetContractorInfo Error " + JsonConvert.SerializeObject(errorData)
                        );
                    }

                    _logger.LogInformation(
                        "SetContractorInfo SendData "
                            + JsonConvert.SerializeObject(groupByIdentificationAndWorkLocation)
                    );

                    return setInfo;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    $"SetContractorInfo Error UnhandledException ${ex.Message} "
                        + JsonConvert.SerializeObject(groupByIdentificationAndWorkLocation)
                );

                return new SetContractorInfoResponse
                {
                    messageKey = "UnhandledException",
                    message = ex.Message,
                };
            }
        }
        #endregion

        #region Checkout
        [HttpPost("SA_ValidateStaffCheckout")]
        public async Task<IActionResult> SA_ValidateStaffCheckout()
        {
            try
            {
                string todayPendingPTWMainFilter =
                    $"filter=WorkDate eq '{DateTime.Now.ToString("yyyy-MM-dd")}' and Step eq 4 and WorkLocationID eq 'LAP'&$top=500"; // Only LAP
                var todayPendingPTWMain = await _sharepointRepository.GetListItem<PTW_Main>(
                    SysConstant.PTW_CSM_SITE,
                    "PTW_Main",
                    todayPendingPTWMainFilter
                );
                List<ValidateStaffResponse> res = new();
                foreach (var ptwMain in todayPendingPTWMain)
                {
                    var employeeJoining =
                        await _sharepointRepository.GetListItem<PTW_EmployeeJoining>(
                            SysConstant.PTW_CSM_SITE,
                            "PTW_EmployeeJoining",
                            $"filter=PTW_ID eq '{ptwMain.PTW_ID}'"
                        );

                    foreach (var emp in employeeJoining)
                    {
                        var accessItem = await _sharepointRepository.GetItem<ContractorStaffAccess>(
                            SysConstant.PTW_CSM_SITE,
                            "ContractorStaffAccess",
                            $"filter=IdentificationNumber eq '{emp.IdentificationNumber}' and WorkLocationID eq '{ptwMain.WorkLocationID}' and ContractorID eq '{ptwMain.ContractorID}'&$top=1"
                        );

                        if (accessItem != null && accessItem.StatusCodeOut != 0)
                        {
                            await _sharepointRepository.UpdateItem<ContractorStaffAccess>(
                                SysConstant.PTW_CSM_SITE,
                                "ContractorStaffAccess",
                                accessItem.ID,
                                new
                                {
                                    StatusCodeOut = 0,
                                    ErrorMessageOut = "Nhân viên còn GPLV chưa đóng",
                                    ModifiedCheckOut = DateTime.Now,
                                }
                            );
                            res.Add(
                                new ValidateStaffResponse
                                {
                                    Message = "",
                                    Status = ValidateStaffStatus.UPDATED,
                                }
                            );
                        }
                        else
                        {
                            res.Add(
                                new ValidateStaffResponse
                                {
                                    Message = "",
                                    Status = ValidateStaffStatus.NO_UPDATE,
                                }
                            );
                        }
                        await Task.Delay(500);
                    }
                }

                var countByStatus = res.GroupBy(res => res.Status)
                    .Select(res => new { Status = res.Key, Count = res.Count() });

                return Ok(new { countByStatus, validateResList = res });
            }
            catch (System.Exception ex)
            {
                return Ok(ex.Message);
            }
        }

        [HttpPost("SA_ActiveOutByPTW")]
        public async Task<IActionResult> SA_ActiveOutByPTW([FromBody] PTW_Main ptwMain)
        {
            if (ptwMain.WorkLocationID != "LAP")
            {
                return Ok("Only LAP");
            }

            try
            {
                List<ValidateStaffResponse> res = new();

                var employeeJoining = await _sharepointRepository.GetListItem<PTW_EmployeeJoining>(
                    SysConstant.PTW_CSM_SITE,
                    "PTW_EmployeeJoining",
                    $"filter=PTW_ID eq '{ptwMain.PTW_ID}'"
                );

                foreach (var emp in employeeJoining)
                {
                    var accessItem = await _sharepointRepository.GetItem<ContractorStaffAccess>(
                        SysConstant.PTW_CSM_SITE,
                        "ContractorStaffAccess",
                        $"filter=IdentificationNumber eq '{emp.IdentificationNumber}' and WorkLocationID eq '{ptwMain.WorkLocationID}' and ContractorID eq '{ptwMain.ContractorID}'&$top=1"
                    );

                    if (accessItem != null && accessItem.StatusCodeOut != 1)
                    {
                        var updatedRes =
                            await _sharepointRepository.UpdateItem<ContractorStaffAccess>(
                                SysConstant.PTW_CSM_SITE,
                                "ContractorStaffAccess",
                                accessItem.ID,
                                new
                                {
                                    StatusCodeOut = 1,
                                    ErrorMessageOut = "",
                                    ModifiedCheckOut = DateTime.Now,
                                }
                            );

                        if (updatedRes.IsSuccessed)
                        {
                            res.Add(
                                new ValidateStaffResponse
                                {
                                    Message = "",
                                    Status = ValidateStaffStatus.UPDATED,
                                }
                            );
                        }
                        else
                        {
                            res.Add(
                                new ValidateStaffResponse
                                {
                                    Message = updatedRes.Message,
                                    Status = ValidateStaffStatus.FAILED,
                                }
                            );
                        }
                    }
                    else
                    {
                        res.Add(
                            new ValidateStaffResponse
                            {
                                Message = "",
                                Status = ValidateStaffStatus.NO_UPDATE,
                            }
                        );
                    }
                    await Task.Delay(500);
                }

                var countByStatus = res.GroupBy(res => res.Status)
                    .Select(res => new { Status = res.Key, Count = res.Count() });

                return Ok(new { countByStatus, validateResList = res });
            }
            catch (System.Exception ex)
            {
                return Ok(ex.Message);
            }
        }

        [HttpPost("SA_ActiveOut")]
        public async Task<IActionResult> SA_ActiveOut()
        {
            var allAccess = await GetAllItems<ContractorStaffAccess>("ContractorStaffAccess");
            List<ValidateStaffResponse> res = new();

            foreach (var accessItem in allAccess)
            {
                if (accessItem.StatusCodeOut != 1 && accessItem.WorkLocationID == "LAP") // Only LAP
                {
                    var updatedRes = await _sharepointRepository.UpdateItem<ContractorStaffAccess>(
                        SysConstant.PTW_CSM_SITE,
                        "ContractorStaffAccess",
                        accessItem.ID,
                        new
                        {
                            StatusCodeOut = 1,
                            ErrorMessage = "",
                            ModifiedCheckOut = DateTime.Now,
                        }
                    );

                    if (updatedRes.IsSuccessed)
                    {
                        res.Add(
                            new ValidateStaffResponse
                            {
                                Message = "",
                                Status = ValidateStaffStatus.UPDATED,
                            }
                        );
                    }
                    else
                    {
                        res.Add(
                            new ValidateStaffResponse
                            {
                                Message = updatedRes.Message,
                                Status = ValidateStaffStatus.FAILED,
                            }
                        );
                    }
                    await Task.Delay(500);
                }
            }

            var countByStatus = res.GroupBy(res => res.Status)
                .Select(res => new { Status = res.Key, Count = res.Count() });

            return Ok(new { countByStatus, validateResList = res });
        }
        #endregion

        #region Other
        [HttpDelete("RemoveAccessByContractorStaffID")]
        public async Task<IActionResult> RemoveAccessByContractorStaffID()
        {
            try
            {
                // Fetch all ContractorStaffAccess records where ContractorStaffID = 0
                var staffAccessList = await GetAllItems<ContractorStaffAccess>(
                    "ContractorStaffAccess"
                );

                var filteredList = staffAccessList
                    .Where(item => item.ContractorStaffID == 0)
                    .ToList();

                // Delete each record
                foreach (var access in filteredList)
                {
                    await _sharepointRepository.DeleteItemAsync<ContractorStaffAccess>(
                        $"{SysConstant.PTW_CSM_SITE}_api/web/lists/getbytitle('ContractorStaffAccess')/items({access.ID})"
                    );
                    await Task.Delay(100); // Optional: delay to prevent overwhelming the server
                }

                return Ok(
                    new
                    {
                        Message = "All ContractorStaffAccess records with ContractorStaffID = 0 have been removed.",
                        Count = filteredList.Count,
                    }
                );
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = "An error occurred.", Error = ex.Message });
            }
        }

        [HttpPost("TEST")]
        public async Task<IActionResult> POST_TEST()
        {
            _logger.LogError("POST_TEST" + JsonConvert.SerializeObject(new { Name = "hi" }));

            return Ok();
        }

        [HttpPost("TEST_SETRES")]
        public IActionResult TEST_SETRES([FromBody] SetContractorInfoResponse raw)
        {
            var res = JsonConvert.DeserializeObject<SetContractorInfoResponse>(
                JsonConvert.SerializeObject(raw)
            );
            return Ok(res.data);
        }

        private async Task<List<ValidateStaffResponse>> HandleAddMissingAccess(
            List<ContractorStaffAccess> staffAccessList,
            List<ProjectStaffs> projectStaffList,
            List<ValidateStaffResponse> validateResList
        )
        {
            var missingAccess = staffAccessList
                .Where(sa =>
                    projectStaffList.Find(ps =>
                        ps.IdentificationNumber == sa.IdentificationNumber
                        && ps.WorkLocationID == sa.WorkLocationID
                        && ps.ContractorID == sa.ContractorID
                    ) == null
                )
                .ToList();

            foreach (var miss in missingAccess)
            {
                // Tạo dữ liệu cập nhật
                var updatedData = new
                {
                    StatusCode = 0,
                    ErrorMessage = "Nhân viên chưa được sắp xếp vào công việc/dự án tại nhà máy",
                    ModifiedCheckIn = DateTime.Now,
                };

                // So sánh dữ liệu cũ với dữ liệu mới
                bool isChanged =
                    miss.StatusCode != updatedData.StatusCode
                    || (miss.ErrorMessage ?? "") != updatedData.ErrorMessage;

                if (isChanged)
                {
                    // Cập nhật khi có sự thay đổi
                    await _sharepointRepository.UpdateItem<ContractorStaffAccess>(
                        SysConstant.PTW_CSM_SITE,
                        "ContractorStaffAccess",
                        miss.ID,
                        updatedData
                    );

                    validateResList.Add(
                        new ValidateStaffResponse { Status = ValidateStaffStatus.UPDATED }
                    );
                }
                else
                {
                    // Không có thay đổi, thêm trạng thái NO_UPDATE
                    validateResList.Add(
                        new ValidateStaffResponse { Status = ValidateStaffStatus.NO_UPDATE }
                    );
                }

                await Task.Delay(500);
            }
            return validateResList;
        }

        [HttpPost("RemoveDupAccess")]
        public async Task<IActionResult> RemoveDupAccess()
        {
            List<ContractorStaffAccess> staffAccessList = await GetAllItems<ContractorStaffAccess>(
                "ContractorStaffAccess"
            );

            var distinctAccess = staffAccessList
                .GroupBy(c => new
                {
                    c.IdentificationNumber,
                    c.WorkLocationID,
                    c.ContractorID,
                })
                .Select(g => g.Last())
                .ToList();

            var dupAccess = staffAccessList
                .GroupBy(c => new
                {
                    c.IdentificationNumber,
                    c.WorkLocationID,
                    c.ContractorID,
                })
                .Where(g => g.Count() > 1)
                .SelectMany(g => g)
                .ToList();

            foreach (var dup in dupAccess)
            {
                if (distinctAccess.Find(d => d.ID == dup.ID) == null)
                {
                    await _sharepointRepository.DeleteItemAsync<ContractorStaffAccess>(
                        $"{SysConstant.PTW_CSM_SITE}_api/web/lists/getbytitle('ContractorStaffAccess')/items({dup.ID})"
                    );
                }
            }

            return Ok(dupAccess);
        }

        [HttpGet("TestBatch")]
        public async Task<IActionResult> TestBatch()
        {
            List<TestList> testList = new List<TestList>()
            {
                new TestList { ID = 35, Title = "1 updated" },
                new TestList { ID = 36, Title = "2 updated" },
            };

            List<JsonObject> testListObjForUpdate = testList
                .Select(item =>
                    Helpers.MakeJsonObject(
                        new
                        {
                            __metadata = new { type = "SP.Data.TESTListItem" },
                            ID = item.ID,
                            Title = item.Title,
                        }
                    )
                )
                .ToList()!;

            List<JsonObject> testListObjForCreate = testList
                .Select(item =>
                    Helpers.MakeJsonObject(
                        new
                        {
                            __metadata = new { type = "SP.Data.TESTListItem" },
                            Title = item.Title,
                        }
                    )
                )
                .ToList()!;

            var resCreate = await _sharepointRepository.BatchCreateAsync<TestList>(
                SysConstant.PTW_CSM_SITE,
                "TEST",
                testListObjForCreate
            );

            var resUpdate = await _sharepointRepository.BatchUpdateAsync<TestList>(
                SysConstant.PTW_CSM_SITE,
                "TEST",
                testListObjForUpdate
            );

            var resDelete = await _sharepointRepository.BatchDeleteAsync<TestList>(
                SysConstant.PTW_CSM_SITE,
                "TEST",
                testListObjForUpdate
            );

            return Ok(resUpdate);
        }

        private async Task<List<T>> GetAllItems<T>(string listName, string? filter = "")
        {
            string nextLink = "";
            bool hasNextLink = true;
            List<T> itemList = new();

            while (hasNextLink)
            {
                try
                {
                    ApiResult<object> dataNextLink;

                    // Determine the appropriate URL to fetch items
                    if (string.IsNullOrEmpty(nextLink))
                    {
                        dataNextLink = await _sharepointRepository.GetItemsAsync(
                            $"{SysConstant.PTW_CSM_SITE}_api/web/lists/getbytitle('{listName}')/items?$filter={filter}&$top=5000"
                        );
                    }
                    else
                    {
                        dataNextLink = await _sharepointRepository.GetItemsAsync(nextLink);
                    }

                    // Parse the result and extract the next link
                    var resultObj = JObject.Parse(dataNextLink.ResultObj?.ToString() ?? "{}");

                    nextLink = resultObj["odata.nextLink"]?.ToString() ?? "";
                    hasNextLink = !string.IsNullOrEmpty(nextLink);

                    // Deserialize the items and add them to the list
                    var parsedItems =
                        JsonConvert.DeserializeObject<List<T>>(
                            resultObj["value"]?.ToString() ?? "[]"
                        ) ?? new();

                    itemList.AddRange(parsedItems);
                }
                catch (Exception ex)
                {
                    _logger.LogError($"GetAllItems Error UnhandledException ${ex.Message} ");
                    // _ = TrackingFacialIDAsync(
                    //     new { listName, filter },
                    //     "GetAllItems",
                    //     "Error",
                    //     ex.Message
                    // );
                    hasNextLink = false; // Terminate the loop if an unhandled error occurs
                }

                // Optional: Delay to avoid overwhelming the server
                await Task.Delay(2000);
            }

            return itemList;
        }
        #endregion

        #region Logs
        [HttpGet("GetLogFiles")]
        public IActionResult GetLogFiles(string date)
        {
            try
            {
                // Validate date format (expecting YYYYMMDD)
                if (string.IsNullOrEmpty(date) || date.Length != 8 || !date.All(char.IsDigit))
                {
                    return BadRequest(
                        "Invalid date format. Expected format: YYYYMMDD (e.g., 20250522)"
                    );
                }

                // Define logs directory path
                string logsDirectory = Path.Combine(Directory.GetCurrentDirectory(), "logs");

                // Create logs directory if it doesn't exist
                if (!Directory.Exists(logsDirectory))
                {
                    Directory.CreateDirectory(logsDirectory);
                    _logger.LogInformation(
                        "Created logs directory at {LogsDirectory}",
                        logsDirectory
                    );
                }

                // Get all log files in the directory
                var logFiles = Directory
                    .GetFiles(logsDirectory, "*.log")
                    .Select(path => new FileInfo(path))
                    .Where(file => file.Name.Contains(date))
                    .OrderByDescending(file => file.LastWriteTime)
                    .Select(file => new LogFileViewModel
                    {
                        FileName = file.Name,
                        FilePath = file.FullName,
                        FileSize = file.Length,
                        LastModified = file.LastWriteTime,
                    })
                    .ToList();

                var response = new LogFilesResponseViewModel
                {
                    Count = logFiles.Count,
                    LogFiles = logFiles,
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError("Error getting log files: {Message}", ex.Message);
                return StatusCode(
                    500,
                    new { Error = "Failed to retrieve log files", Message = ex.Message }
                );
            }
        }

        [HttpGet("GetLogFileContent")]
        public IActionResult GetLogFileContent(string fileName)
        {
            try
            {
                if (string.IsNullOrEmpty(fileName))
                {
                    return BadRequest("File name is required");
                }

                // Sanitize the filename to prevent directory traversal attacks
                fileName = Path.GetFileName(fileName);

                string logsDirectory = Path.Combine(Directory.GetCurrentDirectory(), "logs");
                string filePath = Path.Combine(logsDirectory, fileName);

                if (!System.IO.File.Exists(filePath))
                {
                    return NotFound($"Log file '{fileName}' not found");
                }

                // Read the file content
                var content = System.IO.File.ReadAllBytes(filePath);

                return File(content, "application/octet-stream");
            }
            catch (Exception ex)
            {
                _logger.LogError("Error reading log file: {Message}", ex.Message);
                return StatusCode(
                    500,
                    new { Error = "Failed to read log file", Message = ex.Message }
                );
            }
        }
        #endregion
    }

    public static class ValidateStaffStatus
    {
        public static string UPDATED = "UPDATED";
        public static string CREATED = "CREATED";
        public static string NO_UPDATE = "NO_UPDATE";
        public static string FAILED = "FAILED";
    }

    public class ValidateStaffResponse
    {
        public string Status { get; set; }
        public string Message { get; set; }
        public double? ContractorStaffID { get; set; }
    }

    public class SendAccessToFacialID
    {
        public string? ModifiedFrom { get; set; }
        public string? ModifiedTo { get; set; }
        public string? IdentificationNumber { get; set; } // Changed from ContractorStaffID
    }
}
