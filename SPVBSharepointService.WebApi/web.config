﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <location path="." inheritInChildApplications="false">
    <system.webServer>
      <handlers>
        <add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2"
          resourceType="Unspecified" />
      </handlers>
      <aspNetCore processPath="dotnet" arguments=".\SPVBSharepointService.WebApi.dll"
        stdoutLogEnabled="true" stdoutLogFile=".\logs\stdout" hostingModel="inprocess" />
      <security>
        <requestFiltering>
          <requestLimits maxAllowedContentLength="51200600" /> <!-- 100 MB -->
        </requestFiltering>
      </security>
    </system.webServer>
  </location>
  <system.web>
    <httpRuntime executionTimeout="18000" maxRequestLength="51200" /> <!-- 100 MB -->
  </system.web>
</configuration>