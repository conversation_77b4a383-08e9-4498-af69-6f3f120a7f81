﻿using System.ComponentModel.DataAnnotations;

namespace SPVBSharepointService.WebApi.ViewModels.System.User
{
    public class RegisterRequest
    {
        public string UserName { get; set; }

        [DataType(DataType.Password)]
        public string Password { get; set; }

        [DataType(DataType.Password)]
        public string ConfirmPassword { get; set; }
        public bool IsActive { get; set; } = true;
        public string? Role { get; set; }
        public string? ContractorID { get; set; }
    }
}
