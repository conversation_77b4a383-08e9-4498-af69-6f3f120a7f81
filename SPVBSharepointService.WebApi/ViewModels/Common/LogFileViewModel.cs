using System;

namespace SPVBSharepointService.WebApi.ViewModels.Common
{
    public class LogFileViewModel
    {
        public string FileName { get; set; } = string.Empty;
        public string FilePath { get; set; } = string.Empty;
        public long FileSize { get; set; }
        public DateTime LastModified { get; set; }
    }

    public class LogFileContentViewModel
    {
        public string FileName { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
    }

    public class LogFilesResponseViewModel
    {
        public int Count { get; set; }
        public List<LogFileViewModel> LogFiles { get; set; } = new List<LogFileViewModel>();
    }
}
